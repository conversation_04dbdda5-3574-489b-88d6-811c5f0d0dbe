# 樂趣 (Yuequ)

一个基于 React + TypeScript + Ant Design 的音频资源交易平台，支持中英文国际化。

## 🚀 技术栈

- **前端框架**: React 19 + TypeScript
- **UI 组件库**: Ant Design 5.x
- **样式方案**: Tailwind CSS 4.x
- **状态管理**: Zustand
- **路由管理**: React Router 7.x
- **HTTP 客户端**: Axios
- **国际化**: react-i18next
- **构建工具**: Vite 7.x
- **代码规范**: ESLint + Prettier

## 📦 项目结构

```
audio-marketplace/
├── src/
│   ├── components/          # 通用组件
│   │   └── Layout.tsx      # 应用布局组件（含语言切换）
│   ├── pages/              # 页面组件
│   │   ├── Home.tsx        # 首页（支持i18n）
│   │   ├── About.tsx       # 关于页面（支持i18n）
│   │   └── NotFound.tsx    # 404页面（支持i18n）
│   ├── router/             # 路由配置
│   │   └── index.tsx       # 路由定义
│   ├── store/              # 状态管理
│   │   ├── authStore.ts    # 用户认证状态
│   │   ├── appStore.ts     # 应用全局状态（含语言状态）
│   │   └── index.ts        # Store 导出
│   ├── services/           # API 服务层
│   │   ├── api.ts          # Axios 封装
│   │   └── index.ts        # 服务导出
│   ├── locales/            # 国际化翻译文件
│   │   ├── zh/             # 中文翻译
│   │   │   ├── common.json # 通用翻译
│   │   │   ├── home.json   # 首页翻译
│   │   │   ├── about.json  # 关于页翻译
│   │   │   ├── error.json  # 错误页翻译
│   │   │   └── index.ts    # 中文翻译导出
│   │   ├── en/             # 英文翻译
│   │   │   ├── common.json # 通用翻译
│   │   │   ├── home.json   # 首页翻译
│   │   │   ├── about.json  # 关于页翻译
│   │   │   ├── error.json  # 错误页翻译
│   │   │   └── index.ts    # 英文翻译导出
│   │   └── index.ts        # i18n 主配置
│   ├── types/              # TypeScript 类型定义
│   │   ├── index.ts        # 全局类型
│   │   └── i18n.d.ts       # 国际化类型定义
│   ├── utils/              # 工具函数
│   │   └── constants.ts    # 常量定义
│   └── hooks/              # 自定义 Hooks
│       └── useLanguage.ts  # 语言切换Hook
├── public/                 # 静态资源
├── tailwind.config.js      # Tailwind 配置
├── vite.config.ts          # Vite 配置
├── tsconfig.json           # TypeScript 配置
├── eslint.config.js        # ESLint 配置
├── postcss.config.js       # PostCSS 配置
└── package.json            # 项目依赖
```

## 🛠️ 开发环境配置

### 已完成配置

✅ **ESLint + Prettier**: 代码规范和格式化
✅ **宽松 TypeScript**: 适合渐进式类型检查
✅ **Tailwind CSS**: 实用优先的 CSS 框架
✅ **Zustand**: 轻量级状态管理
✅ **React Router**: 客户端路由
✅ **Axios 封装**: 完整的 HTTP 请求封装
✅ **Ant Design**: 中英文语言包和组件库
✅ **基本路由结构**: 首页、关于页、404页面
✅ **国际化 (i18n)**: 完整的中英文切换支持

### TypeScript 配置说明

项目采用宽松的 TypeScript 配置：

- `strict: false` - 关闭严格模式
- `noImplicitAny: false` - 允许隐式 any 类型
- `strictNullChecks: false` - 关闭严格 null 检查

您可以在学习过程中逐步启用这些严格选项。

## 🌍 国际化 (i18n) 功能

### 支持的语言

- 🇨🇳 中文 (zh)
- 🇺🇸 English (en)

### 使用方法

#### 1. 基本用法

```typescript
import { useTranslation } from 'react-i18next';

const MyComponent = () => {
  const { t } = useTranslation();

  return (
    <div>
      <h1>{t('common.appName')}</h1>
      <p>{t('home.subtitle')}</p>
    </div>
  );
};
```

#### 2. 使用自定义Hook

```typescript
import { useLanguage } from '@/hooks/useLanguage';

const MyComponent = () => {
  const { t, language, changeLanguage } = useLanguage();

  return (
    <div>
      <h1>{t('common.appName')}</h1>
      <button onClick={() => changeLanguage(language === 'zh' ? 'en' : 'zh')}>
        切换语言
      </button>
    </div>
  );
};
```

#### 3. 语言切换

在页面头部已集成语言切换下拉框，支持：

- 自动检测浏览器语言
- 本地存储用户选择
- 动态切换 Ant Design 语言包

#### 4. 添加新翻译

**添加中文翻译** (`src/locales/zh/common.json`):

```json
{
  "newKey": "新的翻译内容"
}
```

**添加英文翻译** (`src/locales/en/common.json`):

```json
{
  "newKey": "New translation content"
}
```

#### 5. 翻译文件结构

- `common.json`: 通用翻译（导航、按钮、表单等）
- `home.json`: 首页相关翻译
- `about.json`: 关于页面翻译
- `error.json`: 错误页面翻译

## 🚦 快速开始

### 安装依赖

```bash
pnpm install
```

### 启动开发服务器

```bash
pnpm dev
```

### 代码格式化

```bash
# 格式化所有文件
pnpm format

# 检查格式
pnpm format:check
```

### 代码检查

```bash
# 运行 ESLint
pnpm lint

# 自动修复 ESLint 问题
pnpm lint:fix

# TypeScript 类型检查
pnpm type-check
```

### 构建项目

```bash
pnpm build
```

### 预览构建结果

```bash
pnpm preview
```

## 🔧 主要特性

### 状态管理 (Zustand)

```typescript
// 用户认证状态
import { useAuthStore } from '@/store';

const { user, isAuthenticated, login, logout } = useAuthStore();

// 应用全局状态（含语言切换）
import { useAppStore } from '@/store';

const { theme, language, setTheme, setLanguage } = useAppStore();
```

### API 请求封装

```typescript
import { ApiService } from '@/services';

// GET 请求
const response = await ApiService.get('/users');

// POST 请求
const response = await ApiService.post('/users', { name: '用户名' });
```

### 路由导航

```typescript
import { useNavigate } from 'react-router-dom';

const navigate = useNavigate();
navigate('/about');
```

### 国际化翻译

```typescript
import { useTranslation } from 'react-i18next';
// 或使用自定义Hook
import { useLanguage } from '@/hooks/useLanguage';

const { t } = useTranslation();
const { tc, changeLanguage } = useLanguage();
```

## 🎨 样式方案

- **Ant Design**: 提供完整的组件库，支持中英文语言包
- **Tailwind CSS**: 提供原子化 CSS 类
- **自定义样式**: 在 `src/index.css` 中定义全局样式

## 📝 开发规范

1. **组件命名**: 使用 PascalCase
2. **文件命名**: 组件文件使用 PascalCase，其他文件使用 camelCase
3. **类型定义**: 统一在 `src/types/` 目录下管理
4. **常量定义**: 统一在 `src/utils/constants.ts` 中管理
5. **API 调用**: 统一使用封装的 ApiService
6. **国际化**: 所有用户可见文本必须使用 t() 函数
7. **翻译文件**: 按功能模块组织，保持中英文键值对应

## 🔜 后续扩展

- [ ] 用户登录注册页面（含i18n）
- [ ] 音频文件管理
- [ ] 文件上传功能
- [ ] 支付集成
- [ ] 用户个人中心
- [ ] 管理员后台
- [ ] 更多语言支持（日语、韩语等）

## 📄 License

MIT License
