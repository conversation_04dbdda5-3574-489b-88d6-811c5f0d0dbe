import { defineConfig } from 'unocss';
import presetUno from '@unocss/preset-uno';
import presetAttributify from '@unocss/preset-attributify';
import presetIcons from '@unocss/preset-icons';
import transformerDirectives from '@unocss/transformer-directives';
import transformerVariantGroup from '@unocss/transformer-variant-group';

export default defineConfig({
  presets: [
    presetUno(), // 基础工具类预设
    presetAttributify(), // 属性模式预设
    presetIcons({
      // 可选：配置图标预设
      warn: true,
    }),
  ],
  transformers: [
    transformerDirectives(), // 支持 @apply 等指令
    transformerVariantGroup(), // 支持变量组语法
  ],
  // 自定义主题颜色和字体 - 使用CSS变量
  theme: {
    colors: {
      // 页面背景色
      'page-bg': 'var(--color-page-bg)',
      // 主色调
      primary: 'var(--color-primary)',
      // form表单label颜色
      label: 'var(--color-label)',
      // form表单项颜色
      'form-item': 'var(--color-form-item)',
      // 按钮文字颜色
      'text-btn': 'var(--color-button-text)',
    },
    fontFamily: {
      // 所有字体都使用相同的字体栈 - 保持设计一致性
      sans: 'var(--font-family)',
      mono: 'var(--font-family)', // 代码字体也使用普通字体
      // 为了向后兼容，保留原有的字体类名
      inter: 'var(--font-family)',
      arial: 'var(--font-family)',
    },
  },
  shortcuts: [
    [
      's-form-selector-hover',
      '[&.ant-select-outlined:not(.ant-select-disabled):not(.ant-select-customize-input):not(.ant-pagination-size-changer):hover_.ant-select-selector]:(!bg-white !border-white)',
    ],
    [
      's-form-input',
      'placeholder:font-inter h-[54px] rounded-md border-none bg-form-item px-5 text-black placeholder:(text-[12px] text-black/25)',
    ],
  ],
  // 确保扫描所有相关文件
  content: {
    filesystem: ['src/**/*.{vue,js,ts,jsx,tsx}', 'index.html'],
  },
});
