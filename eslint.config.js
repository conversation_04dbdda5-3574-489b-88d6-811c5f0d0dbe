import js from '@eslint/js';
import globals from 'globals';
import react from 'eslint-plugin-react';
import reactHooks from 'eslint-plugin-react-hooks';
import reactRefresh from 'eslint-plugin-react-refresh';
import tseslint from 'typescript-eslint';
import unocss from '@unocss/eslint-config/flat';

export default tseslint.config([
  unocss,
  {
    ignores: ['dist', 'node_modules', '*.config.js'],
  },
  {
    files: ['**/*.{ts,tsx}'],
    extends: [
      js.configs.recommended,
      ...tseslint.configs.recommended,
      react.configs.flat.recommended,
    ],
    plugins: {
      'react-hooks': reactHooks,
      'react-refresh': reactRefresh,
    },
    // 添加 react 的 settings
    settings: {
      react: {
        version: 'detect', // 自动检测 React 版本
      },
    },
    languageOptions: {
      ecmaVersion: 2020,
      globals: globals.browser,
    },
    rules: {
      // React Hooks 规则
      ...reactHooks.configs.recommended.rules,

      // React Refresh 规则
      'react-refresh/only-export-components': [
        'warn',
        { allowConstantExport: true },
      ],
      // 强制执行更简洁的 JSX 写法，避免不必要的大括号，这能提高代码可读性，从而更容易发现问题
      'react/jsx-curly-brace-presence': [
        'error',
        { props: 'never', children: 'never', propElementValues: 'always' },
      ],
      // TypeScript 宽松规则
      '@typescript-eslint/no-unused-vars': 'warn',
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/no-non-null-assertion': 'warn',
      '@typescript-eslint/ban-ts-comment': 'warn',

      // 其他友好规则
      'no-console': 'warn',
      'prefer-const': 'warn',

      // 防止 JSX 中的意外文本节点
      'no-sequences': 'error', // 禁止逗号操作符
      'no-trailing-spaces': 'error', // 禁止行尾空格

      // JSX 相关规则
      'jsx-quotes': ['error', 'prefer-double'], // JSX 中使用双引号
    },
  },
]);
