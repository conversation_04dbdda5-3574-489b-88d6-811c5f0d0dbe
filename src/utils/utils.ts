// 检查是否为开发环境
export const isDevelopment = import.meta.env.DEV;

// 获取时区
export const getSystemTimeZone = () => {
  return Intl.DateTimeFormat().resolvedOptions().timeZone;
};

// 解析包含国家代码的手机号
export const parsePhoneNumber = (phone: string) => {
  const phoneArr = `${phone}`.split('~');
  return { phone: +phoneArr[0], phoneCountry: phoneArr[1] || 'CN' };
};

// 如果没输入手机号,返回'',有手机号返回`手机号~国家代码`
export const getPhoneNumber = (phone: string, phoneCountry: string) => {
  return phone ? `${phone}~${phoneCountry}` : '';
};
