export const processPermissions = permissions => {
  const permissionsUrls: string[] = [];
  const btnsPermission: string[] = [];
  const traverse = (perms: typeof permissions) => {
    perms.forEach(perm => {
      if (perm.url && !perm.url.includes('button:')) {
        permissionsUrls.push(perm.url);
      }
      if (perm.url.includes('button:')) {
        btnsPermission.push(perm.url);
      }
      if (perm.children?.length) {
        traverse(perm.children);
      }
    });
  };
  traverse(permissions);
  return {
    permissionsUrls,
    btnsPermission,
  };
};
