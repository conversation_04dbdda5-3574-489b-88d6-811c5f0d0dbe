import { ApiService } from '@/services/request';

/**
 * API使用示例
 * 展示如何使用简化的loading管理系统
 */

// 1. 默认情况：显示loading
export const fetchUserInfo = async () => {
  try {
    const response = await ApiService.get('/user/info');
    return response.data;
  } catch (error) {
    console.error('获取用户信息失败:', error);
    throw error;
  }
};

// 2. 自定义loading文本
export const uploadFile = async (file: File) => {
  try {
    const formData = new FormData();
    formData.append('file', file);

    const response = await ApiService.post('/upload', formData, {
      loadingText: '正在上传文件...',
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  } catch (error) {
    console.error('文件上传失败:', error);
    throw error;
  }
};

// 3. 不显示loading（静默请求）
export const heartbeat = async () => {
  try {
    const response = await ApiService.get('/heartbeat', {
      showLoading: false, // 心跳请求不显示loading
    });
    return response.data;
  } catch (error) {
    console.error('心跳检测失败:', error);
    throw error;
  }
};

// 4. 手动控制loading状态
export const performComplexOperation = async () => {
  try {
    // 显示自定义loading
    ApiService.showLoading('正在处理复杂操作...');

    // 执行一些非API操作
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 执行API调用（这会被加入到同一个loading队列中）
    const result = await ApiService.post('/complex-operation', {
      showLoading: false, // 避免重复loading，因为我们已经手动显示了
    });

    return result.data;
  } catch (error) {
    console.error('复杂操作失败:', error);
    throw error;
  } finally {
    // 确保隐藏loading
    ApiService.hideLoading();
  }
};

// 5. 并发请求示例
export const fetchDashboardData = async () => {
  try {
    // 同时发起多个请求，loading会在所有请求完成后才消失
    const [userInfo, stats, notifications] = await Promise.all([
      ApiService.get('/user/info'),
      ApiService.get('/dashboard/stats'),
      ApiService.get('/notifications', { showLoading: false }), // 通知请求不显示loading
    ]);

    return {
      user: userInfo.data,
      stats: stats.data,
      notifications: notifications.data,
    };
  } catch (error) {
    console.error('获取仪表板数据失败:', error);
    throw error;
  }
};
