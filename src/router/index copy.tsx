import React from 'react';
import {
  createB<PERSON>erRouter,
  RouterProvider,
  Navigate,
  type RouteObject,
} from 'react-router-dom';
import { useAuthStore } from '../store';
import { pageMap, type Permission } from './routeMap';
import Layout from '../components/Layout';
import Home from '../pages/Home';
import Login from '../pages/Login';
import RequireAuth from '../components/RequireAuth';
// 注册流程页面
import Register from '../pages/Register';
import EmailVerification from '../pages/Register/EmailVerification';
import AliasSetup from '../pages/Register/AliasSetup';
import PersonalInfo from '../pages/Register/PersonalInfo';
import PasswordSetup from '../pages/Register/PasswordSetup';

/**
 * 递归地将后端权限树转换为 React Router 的路由配置数组
 * @param permissions - 从后端获取的权限节点数组
 * @returns 一个扁平化的 RouteObject 数组
 */
const buildRoutesFromPermissions = (
  permissions: Permission[],
): RouteObject[] => {
  const routes: RouteObject[] = [];

  const traverse = (nodes: Permission[]) => {
    for (const node of nodes) {
      // 只处理类型为页面（type === 2），并忽略按钮权限
      if (node.type === 2 && node.url && !node.url.startsWith('button:')) {
        const Component = pageMap[node.url];
        if (Component) {
          routes.push({
            path: node.url,
            element: (
              <RequireAuth>
                <Component />
              </RequireAuth>
            ),
          });
        }
      }

      // 递归处理子节点
      if (node.children && node.children.length > 0) {
        traverse(node.children);
      }
    }
  };

  traverse(permissions);
  return routes;
};

const AppRouter: React.FC = () => {
  const { isAuthenticated, permissions } = useAuthStore();

  // 公开路由 - 无需登录即可访问
  const publicRoutes: RouteObject[] = [
    {
      path: '/login',
      element: <Login />,
    },
    // 注册流程路由
    {
      path: '/register',
      element: <Register />,
      children: [
        {
          path: 'email',
          element: <EmailVerification />,
        },
        {
          path: 'alias',
          element: <AliasSetup />,
        },
        {
          path: 'personal-info',
          element: <PersonalInfo />,
        },
        {
          path: 'password',
          element: <PasswordSetup />,
        },
      ],
    },
  ];

  // 受保护的路由 - 需要登录且基于权限动态生成
  const protectedRoutes: RouteObject[] = isAuthenticated
    ? buildRoutesFromPermissions(permissions)
    : [];

  // 基础布局路由
  const layoutRoutes: RouteObject[] = [
    {
      path: '/',
      element: <Layout />,
      children: [
        {
          index: true, // 默认展示Home，所有用户都可以访问
          element: <Home />,
        },
        {
          path: 'profile',
          element: (
            <RequireAuth>
              {React.createElement(pageMap['/profile'])}
            </RequireAuth>
          ),
        },
        // 将基于权限动态生成的路由作为子路由
        ...protectedRoutes,
      ],
    },
  ];

  const router = createBrowserRouter([
    ...publicRoutes,
    ...layoutRoutes,
    {
      path: '*',
      element: <Navigate to="/" replace />,
    },
  ]);

  return <RouterProvider router={router} />;
};

export default AppRouter;
