import React, { useEffect } from 'react';
import { Form, Input, Button } from 'antd';
import { useLanguage } from '@/hooks/useLanguage';
import type { FormInstance } from 'antd/es/form';
import type { LoginFormData } from '@/types';
import FormActions from './FormActions';

interface PasswordInputProps {
  form: FormInstance<LoginFormData>;
  onSubmit: (values: Pick<LoginFormData, 'password'>) => void;
  onSwitchToEmailCode: () => void;
  initialPassword?: string;
}

const PasswordInput: React.FC<PasswordInputProps> = ({
  form,
  onSubmit,
  onSwitchToEmailCode,
  initialPassword = '',
}) => {
  useEffect(() => {
    if (initialPassword) {
      form.setFieldsValue({ password: initialPassword });
    }
  }, [initialPassword, form]);

  const { t } = useLanguage();

  const handleSubmit = () => {
    form
      .validateFields(['password'])
      .then(values => {
        onSubmit(values);
      })
      .catch(errorInfo => {
        console.log('Password validation failed:', errorInfo);
      });
  };

  return (
    <>
      <Form.Item
        label={t('auth.login.form.password')}
        name="password"
        messageVariables={{ label: t('auth.login.form.password') }}
        rules={[{ required: true }]}
        className="mb-6"
      >
        <Input.Password
          placeholder={t('auth.register.step4.form.passwordPlaceholder')}
          className="placeholder:font-inter h-[54px] rounded-md border-none bg-form-item px-5 text-black placeholder:(text-[12px] text-black/25)"
          size="large"
        />
      </Form.Item>
      <FormActions
        buttonText={t('auth.login.form.submit')}
        onSubmit={handleSubmit}
      />
      <div className="mt-6 text-center">
        <span
          className="cursor-pointer text-[12px] text-[#ff5e13] hover:underline"
          onClick={onSwitchToEmailCode}
        >
          {t('auth.login.form.loginWithEmailCode')}
        </span>
      </div>
    </>
  );
};

export default PasswordInput;
