import React from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import Header from '@/components/Header';

const Register: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // 初始化时重定向到第一步
  React.useEffect(() => {
    if (location.pathname === '/register') {
      navigate('/register/email', { replace: true });
    }
  }, [location.pathname, navigate]);

  return (
    <ConfigProvider
      theme={{
        cssVar: true,
        token: {
          colorPrimary: '#ff5e13',
        },
        components: {
          Form: {
            labelFontSize: 12,
          },
          Select: {
            // 禁用状态的背景色
            colorBgContainerDisabled: '#888888',

            // 禁用状态的文字颜色
            colorTextDisabled: '#ffffff',

            colorBgContainer: 'var(--color-form-item)',

            // 激活态边框色
            activeBorderColor: 'var(--color-form-item)',

            // 选项配置----- 开始--
            // 选中项的背景色
            optionActiveBg: 'var(--color-form-item)',
            optionLineHeight: '54px',
            optionHeight: 54,
            // 选项配置----- 结束--
          },
          Button: {
            // colorPrimary: 'var(--color-primary)', // 主按钮颜色
            colorPrimaryHover: 'var(--color-primary-active)', // 主按钮悬停颜色
            colorPrimaryActive: 'var(--color-primary-active)', // 主按钮激活颜色
            primaryColor: 'var(--color-primary-text)', // 主按钮文字颜色
            colorBgContainerDisabled: 'var(--color-primary)', // 主按钮禁用颜色使用完整颜色
            borderColorDisabled: 'transparent', // 主按钮禁用边框颜色设为透明
            colorTextDisabled: 'var(--color-primary-text)', // 主按钮禁用文字颜色
            contentFontSizeLG: 18, // 主按钮大号文字大小
            controlHeightLG: 63,
          },
        },
      }}
    >
      <div className="min-h-screen pt-[120px] flex  items-center justify-center bg-page-bg text-[#656565]">
        <Header />
        <div className="w-496px">
          <Outlet />
        </div>
      </div>
    </ConfigProvider>
  );
};

export default Register;
