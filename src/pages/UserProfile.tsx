import React, { useState } from 'react';
import { ArrowLeftOutlined, EditOutlined } from '@ant-design/icons';
import { Button, Input, message } from 'antd';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

const { TextArea } = Input;

interface UserProfileData {
  alias: string;
  name: string;
  address: string;
  stageName: string;
  artistBio: string;
}

/**
 * 用户资料页面组件
 * 基于Figma设计稿实现的用户资料展示和编辑页面
 */
const UserProfile: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  // 用户资料数据状态
  const [profileData, setProfileData] = useState<UserProfileData>({
    alias: 'shark256',
    name: '<PERSON>',
    address: 'address of 59 characters in length',
    stageName: '<PERSON>',
    artistBio:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Curabitur aliquet, nunc vel tincidunt facilisis, erat massa hendrerit erat, ac luctus magna lorem a nulla. Integer eget dui ac arcu hendrerit dignissim.',
  });

  // 编辑状态管理
  const [editingField, setEditingField] = useState<
    keyof UserProfileData | null
  >(null);
  const [tempValue, setTempValue] = useState('');

  /**
   * 开始编辑字段
   */
  const startEdit = (field: keyof UserProfileData) => {
    setEditingField(field);
    setTempValue(profileData[field]);
  };

  /**
   * 保存编辑
   */
  const saveEdit = () => {
    if (editingField) {
      setProfileData(prev => ({
        ...prev,
        [editingField]: tempValue,
      }));
      setEditingField(null);
      setTempValue('');
      message.success('更新成功');
    }
  };

  /**
   * 取消编辑
   */
  const cancelEdit = () => {
    setEditingField(null);
    setTempValue('');
  };

  /**
   * 处理密码修改
   */
  const handleChangePassword = () => {
    message.info('密码修改功能开发中');
  };

  /**
   * 处理退出登录
   */
  const handleLogout = () => {
    message.success('已退出登录');
    navigate('/login');
  };

  /**
   * 渲染编辑字段组件
   */
  const renderEditableField = (
    label: string,
    field: keyof UserProfileData,
    isTextArea: boolean = false,
  ) => {
    const isEditing = editingField === field;

    return (
      <div className="relative w-full bg-white">
        <div className="relative box-border w-full flex flex-col items-start justify-start gap-2.5 p-2.5">
          <div className="relative box-border h-[26px] w-full flex flex-row items-center justify-start gap-2.5 overflow-clip bg-white px-2.5 py-0">
            {/* Label */}
            <div className="relative box-border h-[26px] w-[355px] flex flex-row items-start justify-start gap-2.5 overflow-clip bg-white py-[5px] pl-[60px] pr-[5px]">
              <div className="relative h-4 w-[290px] flex flex-col justify-center text-right text-[16px] text-black font-medium leading-none not-italic">
                <p className="block leading-normal">{label}:</p>
              </div>
            </div>

            {/* Value/Input */}
            <div className="relative box-border flex flex-1 flex-row items-center justify-start gap-2.5 py-[5px] pl-[5px] pr-2.5">
              {isEditing ? (
                <div className="w-full flex gap-2">
                  {isTextArea ? (
                    <TextArea
                      value={tempValue}
                      onChange={e => setTempValue(e.target.value)}
                      className="flex-1"
                      rows={4}
                      autoFocus
                    />
                  ) : (
                    <Input
                      value={tempValue}
                      onChange={e => setTempValue(e.target.value)}
                      className="flex-1"
                      autoFocus
                    />
                  )}
                  <Button size="small" type="primary" onClick={saveEdit}>
                    保存
                  </Button>
                  <Button size="small" onClick={cancelEdit}>
                    取消
                  </Button>
                </div>
              ) : (
                <>
                  {isTextArea ? (
                    <div className="min-h-[100px] w-full border border-gray-200 p-2">
                      <div className="whitespace-pre-wrap text-left text-[16px] text-black font-normal leading-normal not-italic">
                        {profileData[field]}
                      </div>
                    </div>
                  ) : (
                    <div className="h-4 flex flex-1 flex-col justify-center text-left text-[16px] text-black font-normal leading-none not-italic">
                      <p className="block leading-normal">
                        {profileData[field]}
                      </p>
                    </div>
                  )}

                  {/* Edit Button */}
                  <button
                    className="relative box-border flex flex-col cursor-pointer items-center justify-center gap-2.5 overflow-clip bg-white p-1"
                    onClick={() => startEdit(field)}
                  >
                    <div className="relative size-6">
                      <EditOutlined className="text-lg" />
                    </div>
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
        <div className="pointer-events-none absolute inset-x-0 bottom-0 border-t border-gray-300 border-opacity-20" />
      </div>
    );
  };

  return (
    <div className="relative min-h-screen bg-gray-100 bg-opacity-60">
      {/* Modal Container */}
      <div className="absolute left-1/2 top-4 box-border h-[976px] max-w-[95vw] w-[1008px] flex flex-col transform items-start justify-start gap-2.5 overflow-clip rounded-[3px] bg-white p-0 shadow-lg -translate-x-1/2">
        {/* Navigation Bar */}
        <div className="relative h-[72px] w-full border-b border-gray-300 border-opacity-20 rounded-t-[3px] bg-white">
          <div className="relative box-border h-[72px] w-full flex flex-row items-center justify-between gap-2.5 px-4">
            {/* Back Button */}
            <button
              className="flex cursor-pointer items-center gap-2 text-[16px] text-black transition-colors hover:text-blue-600"
              onClick={() => navigate(-1)}
            >
              <ArrowLeftOutlined />
              <span>返回</span>
            </button>

            {/* Title */}
            <div className="flex-1 text-center">
              <h1 className="text-[20px] text-black font-semibold leading-normal">
                我的资料
              </h1>
            </div>

            {/* Spacer for centering */}
            <div className="w-[80px]" />
          </div>
        </div>

        {/* Content */}
        <div className="relative box-border w-full flex flex-1 flex-col items-center justify-start gap-2.5 overflow-y-auto bg-white p-2.5">
          {/* Avatar Section */}
          <div className="relative box-border w-full flex flex-col items-center justify-center gap-2.5 overflow-clip bg-white p-2.5">
            <div className="relative box-border flex flex-col items-center justify-center overflow-clip bg-white p-0">
              <div className="relative size-[210px]">
                {/* Avatar Circle */}
                <div className="h-[210px] w-[210px] flex items-center justify-center rounded-full bg-pink-400">
                  <span className="text-[24px] text-black font-medium">
                    Avatar
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Profile Fields */}
          <div className="w-full space-y-0">
            {renderEditableField('别名', 'alias')}
            {renderEditableField('姓名', 'name')}
            {renderEditableField('地址', 'address')}
            {renderEditableField('艺名', 'stageName')}
            {renderEditableField('艺人简介', 'artistBio', true)}
          </div>

          {/* Change Password Button */}
          <button
            className="relative h-[72px] w-full cursor-pointer border-t border-gray-300 border-opacity-20 bg-white"
            onClick={handleChangePassword}
          >
            <div className="relative box-border h-[72px] w-full flex flex-col items-center justify-center p-3">
              <div className="relative h-[59px] max-w-full w-[430px] flex items-center justify-center border border-gray-300 border-opacity-20 rounded-[36px] bg-blue-300 transition-colors hover:bg-blue-400">
                <span className="whitespace-nowrap text-[16px] text-black font-medium">
                  修改密码
                </span>
              </div>
            </div>
          </button>

          {/* Logout Button */}
          <div className="relative h-[72px] w-full border-t border-gray-300 border-opacity-20 bg-white">
            <div className="relative box-border h-[72px] w-full flex flex-col items-center justify-center p-3">
              <button
                className="relative h-[59px] max-w-full w-[430px] flex items-center justify-center border border-gray-300 border-opacity-20 rounded-[36px] bg-red-500 transition-colors hover:bg-red-600"
                onClick={handleLogout}
              >
                <span className="whitespace-nowrap text-[16px] text-white font-medium">
                  退出登录
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserProfile;
