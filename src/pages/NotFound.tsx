import React from 'react';
import { Button, Result } from 'antd';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '@/hooks/useLanguage';
import { HomeOutlined, ArrowLeftOutlined } from '@ant-design/icons';

/**
 * 404 页面未找到组件
 */
const NotFound: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useLanguage();

  const handleBackHome = () => {
    navigate('/', { replace: true });
  };

  const handleGoBack = () => {
    navigate(-1);
  };

  return (
    <div className="min-h-screen flex items-center justify-center from-blue-50 to-indigo-100 bg-gradient-to-br p-4">
      <div className="max-w-2xl w-full">
        <Result
          status="404"
          title={
            <div className="mb-4 text-6xl text-blue-600 font-bold">
              {t('error.404.title')}
            </div>
          }
          subTitle={
            <div className="mb-8 text-xl text-gray-600">
              {t('error.404.subtitle')}
            </div>
          }
          extra={
            <div className="flex flex-col justify-center gap-4 sm:flex-row">
              <Button
                type="primary"
                size="large"
                icon={<HomeOutlined />}
                onClick={handleBackHome}
                className="h-auto border-blue-500 bg-blue-500 px-8 py-2 hover:border-blue-600 hover:bg-blue-600"
              >
                {t('error.404.backHome')}
              </Button>
              <Button
                size="large"
                icon={<ArrowLeftOutlined />}
                onClick={handleGoBack}
                className="h-auto border-blue-500 px-8 py-2 text-blue-500 hover:border-blue-600 hover:text-blue-600"
              >
                {t('error.404.goBack')}
              </Button>
            </div>
          }
          className="mx-4 rounded-2xl bg-white p-8 shadow-lg"
        />

        {/* 装饰性元素 */}
        <div className="mt-12 text-center">
          <div className="inline-flex items-center gap-2 text-sm text-gray-500">
            <div className="h-2 w-2 animate-pulse rounded-full bg-blue-400"></div>
            <span>
              {t('common.appName')} - {t('common.slogan')}
            </span>
            <div className="h-2 w-2 animate-pulse rounded-full bg-blue-400"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotFound;
