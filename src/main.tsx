import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import 'virtual:uno.css'; // 导入UnoCSS生成的样式
import './index.css';
import App from './App.tsx';
import TagManager from 'react-gtm-module';
import '@ant-design/v5-patch-for-react-19';

// 初始化 GTM - 只在生产环境
if (import.meta.env.VITE_ENABLE_GTM === 'true') {
  TagManager.initialize({
    gtmId: import.meta.env.VITE_GTM_ID || 'G-HW5EPJ59V3',
  });
}

createRoot(document.getElementById('root')!).render(
  // <StrictMode>
  //   <App />
  // </StrictMode>
  <App />
);
