import { create } from 'zustand';
import { persist, devtools } from 'zustand/middleware';
import type {} from '@redux-devtools/extension'; // devtools 类型支持
import type { RegisterFormData } from '@/types';

interface RegisterState {
  formData: RegisterFormData;
  updateFormData: (data: Partial<RegisterFormData>) => void;
  clearFormData: () => void;
}

export const useRegisterStore = create<RegisterState>()(
  devtools(
    persist(
      (set, get) => ({
        formData: {},
        updateFormData: data => {
          set({
            formData: { ...get().formData, ...data },
          });
        },
        clearFormData: () => {
          set({ formData: {} });
        },
      }),
      {
        name: 'register-store', // localStorage持久化的名称
      }
    ),
    {
      name: 'RegisterStore', // DevTools 中显示的名称
    }
  )
);
