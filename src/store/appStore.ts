import { create } from 'zustand';
import { persist, devtools } from 'zustand/middleware';
import type {} from '@redux-devtools/extension'; // devtools 类型支持
import i18n from '../locales';

// 主题类型
export type Theme = 'light' | 'dark';

// 语言类型
export type Language = 'zh' | 'en';

// 应用状态类型
interface AppState {
  theme: Theme;
  language: Language;
  sidebarCollapsed: boolean;
  loading: boolean;
  error: string | null;
}

// 应用操作类型
interface AppActions {
  setTheme: (theme: Theme) => void;
  setLanguage: (language: Language) => void;
  toggleSidebar: () => void;
  setSidebarCollapsed: (collapsed: boolean) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
}

// 创建应用 store
export const useAppStore = create<AppState & AppActions>()(
  devtools(
    persist(
      (set, get) => ({
        // 初始状态
        theme: 'light',
        language: 'zh',
        sidebarCollapsed: false,
        loading: false,
        error: null,

        // 设置主题
        setTheme: (theme: Theme) => {
          set({ theme });

          // 添加到 body 类名中
          document.body.className = document.body.className.replace(
            /theme-\w+/g,
            '',
          );
          document.body.classList.add(`theme-${theme}`);
        },

        // 设置语言
        setLanguage: (language: Language) => {
          set({ language });
          // 更新 i18n 语言
          i18n.changeLanguage(language);
        },

        // 切换侧边栏
        toggleSidebar: () => {
          set(state => ({ sidebarCollapsed: !state.sidebarCollapsed }));
        },

        // 设置侧边栏状态
        setSidebarCollapsed: (collapsed: boolean) => {
          set({ sidebarCollapsed: collapsed });
        },

        // 设置加载状态
        setLoading: (loading: boolean) => {
          set({ loading });
        },

        // 设置错误信息
        setError: (error: string | null) => {
          set({ error });
        },

        // 清除错误信息
        clearError: () => {
          set({ error: null });
        },
      }),
      {
        name: 'app-store',
        // 初始化后同步语言设置
        onRehydrateStorage: () => state => {
          if (state?.theme) {
            document.body.className = document.body.className.replace(
              /theme-\w+/g,
              '',
            );
            document.body.classList.add(`theme-${state.theme}`);
          }
        },
      },
    ),
    {
      name: 'AppStore', // DevTools 中显示的名称
    },
  ),
);
