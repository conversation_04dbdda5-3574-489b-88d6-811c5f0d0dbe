import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import { isDevelopment } from '@/utils/utils';
// LanguageDetector 自动缓存语言，刷新页面自动应用语言

// 导入翻译资源
import zh from './zh.json';
import en from './en.json';

// 支持的语言列表
export const SUPPORTED_LANGUAGES = [
  { code: 'zh', name: '中文' },
  { code: 'en', name: 'English' },
];

// 翻译资源
const resources = {
  zh: {
    translation: zh,
  },
  en: {
    translation: en,
  },
};

// 初始化 i18next
i18n
  .use(LanguageDetector) // 自动检测语言
  .use(initReactI18next) // 传递 i18n 到 react-i18next
  .init({
    resources,
    fallbackLng: 'zh', // 默认语言
    // debug: isDevelopment,

    // 插值配置
    interpolation: {
      escapeValue: false, // React 已经进行了 XSS 防护
    },

    // 语言检测配置
    detection: {
      // 检测顺序
      order: ['localStorage', 'sessionStorage', 'navigator', 'htmlTag'],
      // 缓存用户选择的语言
      caches: ['localStorage', 'sessionStorage'],
      // localStorage 键名
      lookupLocalStorage: 'language',
      // sessionStorage 键名
      lookupSessionStorage: 'language',
    },
  });

export default i18n;
