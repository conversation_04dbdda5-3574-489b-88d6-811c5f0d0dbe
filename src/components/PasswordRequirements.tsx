import { useLanguage } from '@/hooks/useLanguage';
import { Typography } from 'antd';
import React from 'react';

const { Text } = Typography;

interface PasswordRequirementsProps {
  password?: string;
}

const PasswordRequirements: React.FC<PasswordRequirementsProps> = ({
  password = '',
}) => {
  const { t } = useLanguage();

  const passwordRequirements = [
    {
      key: 'length',
      text: t('auth.register.step4.form.passwordRequirements.length'),
      check: (pwd: string) => pwd.length >= 8,
    },
    {
      key: 'uppercase',
      text: t('auth.register.step4.form.passwordRequirements.uppercase'),
      check: (pwd: string) => /[A-Z]/.test(pwd),
    },
    {
      key: 'lowercase',
      text: t('auth.register.step4.form.passwordRequirements.lowercase'),
      check: (pwd: string) => /[a-z]/.test(pwd),
    },
    {
      key: 'number',
      text: t('auth.register.step4.form.passwordRequirements.number'),
      check: (pwd: string) => /[0-9]/.test(pwd),
    },
    {
      key: 'special',
      text: t('auth.register.step4.form.passwordRequirements.special'),
      check: (pwd: string) => /[!@#$%^&*()_+={}[\];':"\\|,.<>/?-]/.test(pwd),
    },
  ];

  return (
    <div className="mb-6">
      {passwordRequirements.map(req => (
        <div key={req.key} className="mb-2 flex items-center">
          <div
            className={`mr-3 h-2 w-2 flex-shrink-0 rounded-full ${
              !password
                ? 'bg-label'
                : req.check(password)
                  ? 'bg-[#00CA47]'
                  : 'bg-#F80000'
            }`}
          />
          <Text
            className={`text-sm ${
              !password
                ? 'text-label'
                : req.check(password)
                  ? 'text-[#00CA47]'
                  : 'text-#F80000'
            }`}
          >
            {req.text}
          </Text>
        </div>
      ))}
    </div>
  );
};

export default PasswordRequirements;
