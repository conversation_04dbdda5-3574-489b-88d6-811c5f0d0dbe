import React, { useState } from 'react';
import { Button, Card, Space, Typography, Input, Form, message } from 'antd';
import { api, authUtils } from '@/services';

const { Title, Text, Paragraph } = Typography;

/**
 * API 测试组件
 * 用于测试和验证API集成的正确性
 */
export const ApiTest: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<any[]>([]);

  const addResult = (title: string, data: any, success: boolean = true) => {
    setResults(prev => [...prev, {
      id: Date.now(),
      title,
      data,
      success,
      timestamp: new Date().toLocaleTimeString()
    }]);
  };

  // 测试检查用户名可用性
  const testCheckUsername = async () => {
    setLoading(true);
    try {
      const result = await api.auth.checkUsername('<EMAIL>');
      addResult('检查用户名可用性', result);
      message.success('检查用户名可用性成功');
    } catch (error) {
      addResult('检查用户名可用性', error, false);
      message.error('检查用户名可用性失败');
    } finally {
      setLoading(false);
    }
  };

  // 测试检查别名可用性
  const testCheckAlias = async () => {
    setLoading(true);
    try {
      const result = await api.auth.checkAlias('testuser');
      addResult('检查别名可用性', result);
      message.success('检查别名可用性成功');
    } catch (error) {
      addResult('检查别名可用性', error, false);
      message.error('检查别名可用性失败');
    } finally {
      setLoading(false);
    }
  };

  // 测试发送验证码
  const testSendOtp = async () => {
    setLoading(true);
    try {
      const result = await api.auth.sendSignupOtp({
        recipient: '<EMAIL>'
      });
      addResult('发送注册验证码', result);
      message.success('发送验证码成功');
    } catch (error) {
      addResult('发送注册验证码', error, false);
      message.error('发送验证码失败');
    } finally {
      setLoading(false);
    }
  };

  // 测试用户注册
  const testSignup = async () => {
    setLoading(true);
    try {
      const result = await api.auth.signup({
        username: '<EMAIL>',
        password: 'TestPassword123!',
        alias: 'testuser',
        profile: {
          firstName: 'Test',
          lastName: 'User',
          bio: 'Test user for API testing'
        },
        defaultRoleId: 'account.role.investor'
      });
      addResult('用户注册', result);

      if (result.code === 200) {
        authUtils.handleLoginSuccess(result.body);
        message.success('用户注册成功');
      }
    } catch (error) {
      addResult('用户注册', error, false);
      message.error('用户注册失败');
    } finally {
      setLoading(false);
    }
  };

  // 测试用户登录
  const testLogin = async () => {
    setLoading(true);
    try {
      const result = await api.auth.login({
        username: '<EMAIL>',
        password: 'TestPassword123!'
      });
      addResult('用户登录', result);

      if (result.code === 200) {
        authUtils.handleLoginSuccess(result.body);
        message.success('用户登录成功');
      }
    } catch (error) {
      addResult('用户登录', error, false);
      message.error('用户登录失败');
    } finally {
      setLoading(false);
    }
  };

  // 测试获取用户资料
  const testGetProfile = async () => {
    setLoading(true);
    try {
      const result = await api.user.getProfile();
      addResult('获取用户资料', result);
      message.success('获取用户资料成功');
    } catch (error) {
      addResult('获取用户资料', error, false);
      message.error('获取用户资料失败');
    } finally {
      setLoading(false);
    }
  };

  // 测试更新用户资料
  const testUpdateProfile = async () => {
    setLoading(true);
    try {
      const result = await api.user.updateProfile({
        bio: `Updated at ${new Date().toISOString()}`,
        stageName: 'Test Stage Name'
      });
      addResult('更新用户资料', result);

      if (result.code === 200 && result.body.trueOrFalse) {
        message.success('更新用户资料成功');
      }
    } catch (error) {
      addResult('更新用户资料', error, false);
      message.error('更新用户资料失败');
    } finally {
      setLoading(false);
    }
  };

  // 清除结果
  const clearResults = () => {
    setResults([]);
  };

  // 检查认证状态
  const checkAuthStatus = () => {
    const isAuthenticated = authUtils.isAuthenticated();
    const userInfo = authUtils.getUserInfo();
    const token = authUtils.getToken();

    addResult('认证状态检查', {
      isAuthenticated,
      userInfo,
      token: token ? `${token.substring(0, 20)}...` : null
    });
  };

  // 登出
  const logout = () => {
    authUtils.logout();
    addResult('用户登出', { message: '已清除本地认证信息' });
    message.success('已登出');
  };

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <Title level={2}>API 集成测试</Title>
      <Paragraph>
        这个页面用于测试新集成的API服务。点击下面的按钮来测试各种API功能。
      </Paragraph>

      <Card title="认证相关API测试" style={{ marginBottom: '16px' }}>
        <Space wrap>
          <Button onClick={testCheckUsername} loading={loading}>
            检查用户名可用性
          </Button>
          <Button onClick={testCheckAlias} loading={loading}>
            检查别名可用性
          </Button>
          <Button onClick={testSendOtp} loading={loading}>
            发送验证码
          </Button>
          <Button onClick={testSignup} loading={loading} type="primary">
            用户注册
          </Button>
          <Button onClick={testLogin} loading={loading} type="primary">
            用户登录
          </Button>
        </Space>
      </Card>

      <Card title="用户资料API测试" style={{ marginBottom: '16px' }}>
        <Space wrap>
          <Button onClick={testGetProfile} loading={loading}>
            获取用户资料
          </Button>
          <Button onClick={testUpdateProfile} loading={loading}>
            更新用户资料
          </Button>
        </Space>
      </Card>

      <Card title="认证状态管理" style={{ marginBottom: '16px' }}>
        <Space wrap>
          <Button onClick={checkAuthStatus}>
            检查认证状态
          </Button>
          <Button onClick={logout} danger>
            登出
          </Button>
        </Space>
      </Card>

      <Card title="测试结果"
            extra={
              <Button onClick={clearResults} size="small">
                清除结果
              </Button>
            }>
        {results.length === 0 ? (
          <Text type="secondary">暂无测试结果</Text>
        ) : (
          <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
            {results.map(result => (
              <Card
                key={result.id}
                size="small"
                style={{
                  marginBottom: '8px',
                  borderColor: result.success ? '#52c41a' : '#ff4d4f'
                }}
                title={
                  <Space>
                    <Text strong>{result.title}</Text>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {result.timestamp}
                    </Text>
                  </Space>
                }
              >
                <pre style={{
                  fontSize: '12px',
                  margin: 0,
                  whiteSpace: 'pre-wrap',
                  maxHeight: '200px',
                  overflow: 'auto'
                }}>
                  {JSON.stringify(result.data, null, 2)}
                </pre>
              </Card>
            ))}
          </div>
        )}
      </Card>
    </div>
  );
};

export default ApiTest;
