import React, { useEffect, useMemo, useRef } from 'react';
import { type FormInstance, Select, Form, ConfigProvider } from 'antd';
import { useLanguage } from '@/hooks/useLanguage';
import { countryRegionData } from './countryRegionData';
// import styles from './CountryRegionSelector.module.css';

interface RegionSelectorProps {
  placeholder?: string;
  size?: 'small' | 'middle' | 'large';
  form: FormInstance;
  countryFieldName?: string; // 允许自定义 country 字段名
  regionFieldName?: string; // 允许自定义 region 字段名
  value?: string;
  onChange?: (value: string) => void;
}

const RegionSelector: React.FC<RegionSelectorProps> = ({
  placeholder,
  size = 'large',
  form,
  countryFieldName = 'country',
  regionFieldName = 'region',
  value,
  onChange,
}) => {
  const { t, isEnUS } = useLanguage();
  const country = Form.useWatch(countryFieldName, form);
  const options = useMemo(() => {
    const region =
      countryRegionData.find(item => item.value === country)?.children || [];
    const result = region.map(item => ({
      label: isEnUS ? item.label.en : item.label.zh,
      value: item.value,
    }));
    console.log('result---', result);

    return result;
  }, [country, form]);

  return (
    <Select
      placeholder={placeholder || t('common.form.selectRegion')}
      size={size}
      className="s-form-selector-hover !h-54px text-14px flex-shrink-0 flex-basis-88px "
      options={options}
      value={value}
      onChange={onChange}
      disabled={!country} // 当没有选择国家时禁用
    />
  );
};

export default RegionSelector;
