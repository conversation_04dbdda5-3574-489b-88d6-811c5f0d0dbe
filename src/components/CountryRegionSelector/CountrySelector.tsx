import React from 'react';
import { Select } from 'antd';
import { useLanguage } from '@/hooks/useLanguage';
import { countryRegionData } from './countryRegionData';
import styles from './CountryRegionSelector.module.css';

interface CountrySelectorProps {
  placeholder?: string;
  size?: 'small' | 'middle' | 'large';
  value?: string;
  onChange?: (value: string) => void;
}
const CountrySelector: React.FC<CountrySelectorProps> = ({
  placeholder,
  size = 'large',
  value,
  onChange,
}) => {
  const { t, isEnUS } = useLanguage();
  const options = countryRegionData.map(item => ({
    label: isEnUS ? item.label.en : item.label.zh,
    value: item.value,
  }));
  return (
    // <div className="s-form-selector-hover">
    <Select
      placeholder={placeholder || t('common.form.selectCountry')}
      size={size}
      className="s-form-selector-hover !h-54px text-14px flex-shrink-0 flex-basis-88px"
      options={options}
      value={value}
      onChange={onChange}
    />
    // </div>
  );
};

export default CountrySelector;
