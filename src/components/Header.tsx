import React from 'react';
import { Layout as AntLayout, Button, Avatar, Dropdown } from 'antd';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '@/store/authStore';
import { useLanguage } from '@/hooks/useLanguage';
import SelectLanguage from '@/components/SelectLanguage';
import logoIcon from '@/assets/logo.svg';
import {
  SoundOutlined,
  UserOutlined,
  LogoutOutlined,
  SettingOutlined,
} from '@ant-design/icons';

const { Header: AntHeader } = AntLayout;
interface HeaderProps {
  fixed?: boolean;
}

const Header: React.FC<HeaderProps> = ({ fixed = true }) => {
  const navigate = useNavigate();
  const { t } = useLanguage();
  const { user, isAuthenticated, permissions } = useAuthStore();
  const handleLogin = () => {
    navigate('/login');
  };

  const handleRegister = () => {
    navigate('/register');
  };

  // 用户菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: t('common.navigation.profile'),
      onClick: () => navigate('/profile'),
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: t('common.navigation.settings'),
      onClick: () => navigate('/settings'),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: t('common.navigation.logout'),
      onClick: useAuthStore.getState().logout,
    },
  ];
  return (
    <header
      className={`flex items-center px-60px bg-page-bg justify-between shadow-sm h-120px  ${
        fixed ? 'fixed top-0 left-0 right-0 z-50' : ''
      } font-700 text-label text-16px `}
    >
      <div
        className="flex items-center cursor-pointer"
        onClick={() => navigate('/')}
      >
        <img src={logoIcon} alt="logo" className="w-54px h-54px" />
      </div>

      <div
        className="flex items-center space-x-4"
        style={{
          fontSize: '16px',
          fontWeight: '700',
          color: 'var(--color-label)',
        }}
      >
        <div className="cursor-pointer hover:text-primary w-120px text-center">
          Help
        </div>
        <div className="cursor-pointer hover:text-primary w-120px text-center">
          About Us
        </div>
        {/* 语言切换下拉框 */}
        <SelectLanguage className="w-120px  text-center" />
        {isAuthenticated ? (
          <div>
            <Avatar
              src={user?.avatar}
              icon={!user?.avatar && <UserOutlined />}
              className="mr-2"
            />
            <span className="text-gray-700">{user?.alias}</span>
          </div>
        ) : (
          <div className="space-x-2 flex items-center border-l-2 border-l-label border-l-solid">
            <div
              className="w-120px h-44px flex items-center justify-center cursor-pointer hover:text-primary"
              onClick={handleLogin}
            >
              {t('common.navigation.login')}
            </div>
            <Button
              type="primary"
              onClick={handleRegister}
              className="w-120px h-44px text-16px font-700"
            >
              {t('common.navigation.register')}
            </Button>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
