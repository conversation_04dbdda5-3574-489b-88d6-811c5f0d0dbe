export type { ApiResponse } from '@/types/api';
// 导出基础服务
export { ApiService } from './request';
export { default as apiClient } from './request';
export { loadingManager } from './request';

// 导出认证相关服务
import { authApi, authUtils, default as auth } from './auth';
export { authApi, authUtils, auth };

// 导出用户相关服务
import { userApi, userUtils, default as user } from './user';
export { userApi, userUtils, user };

// 组装新的API对象，推荐使用这种方式
export const api = {
  auth: authApi,
  user: userApi,
};

// 保持向后兼容的旧API对象
export const legacyApi = {
  user,
  // product,
  // order,
};

// 导出所有API类型
export * from '@/types/api';
