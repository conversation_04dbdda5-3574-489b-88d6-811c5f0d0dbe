import { ApiService } from './request';
import type {
  SignupRequest,
  LoginRequest,
  OtpLoginRequest,
  SendOtpRequest,
  VerifyOtpRequest,
  AuthResponse,
  BooleanResponse,
  AliasCheckResponse,
  ApiResponse,
} from '@/types/api';
import { API_ENDPOINTS } from '@/types/api';

/**
 * 认证相关 API 服务
 */
export const authApi = {
  /**
   * 用户注册
   * @param params 注册参数
   * @returns 认证响应
   */
  async signup(params: SignupRequest): Promise<ApiResponse<AuthResponse>> {
    return ApiService.post(API_ENDPOINTS.AUTH.SIGNUP, params);
  },

  /**
   * 用户登录
   * @param params 登录参数
   * @returns 认证响应
   */
  async login(params: LoginRequest): Promise<ApiResponse<AuthResponse>> {
    return ApiService.post(API_ENDPOINTS.AUTH.LOGIN, params);
  },

  /**
   * 验证码登录
   * @param params 验证码登录参数
   * @returns 认证响应
   */
  async loginWithOtp(
    params: OtpLoginRequest
  ): Promise<ApiResponse<AuthResponse>> {
    return ApiService.post(API_ENDPOINTS.AUTH.LOGIN_OTP, params);
  },

  /**
   * 发送注册验证码
   * @param params 发送验证码参数
   * @returns 布尔响应
   */
  async sendSignupOtp(
    params: SendOtpRequest
  ): Promise<ApiResponse<BooleanResponse>> {
    return ApiService.post(API_ENDPOINTS.AUTH.SEND_SIGNUP_OTP, params);
  },

  /**
   * 发送登录验证码
   * @param params 发送验证码参数
   * @returns 布尔响应
   */
  async sendLoginOtp(
    params: SendOtpRequest
  ): Promise<ApiResponse<BooleanResponse>> {
    return ApiService.post(API_ENDPOINTS.AUTH.SEND_LOGIN_OTP, params);
  },

  /**
   * 发送更改用户名验证码
   * @param params 发送验证码参数
   * @returns 布尔响应
   */
  async sendChangeUsernameOtp(
    params: SendOtpRequest
  ): Promise<ApiResponse<BooleanResponse>> {
    return ApiService.post(API_ENDPOINTS.AUTH.SEND_CHANGE_USERNAME_OTP, params);
  },

  /**
   * 验证OTP验证码
   * @param params 验证参数
   * @returns 布尔响应
   */
  async verifyOtp(
    params: VerifyOtpRequest
  ): Promise<ApiResponse<BooleanResponse>> {
    return ApiService.put(API_ENDPOINTS.AUTH.VERIFY_OTP, params);
  },

  /**
   * 检查用户名可用性
   * @param username 用户名
   * @returns 布尔响应
   */
  async checkUsername(username: string): Promise<ApiResponse<BooleanResponse>> {
    return ApiService.get(
      `${API_ENDPOINTS.AUTH.CHECK_USERNAME}?username=${encodeURIComponent(username)}`
    );
  },

  /**
   * 检查别名可用性
   * @param alias 别名
   * @returns 别名检查响应
   */
  async checkAlias(alias: string): Promise<ApiResponse<AliasCheckResponse>> {
    return ApiService.get(
      `${API_ENDPOINTS.AUTH.CHECK_ALIAS}?alias=${encodeURIComponent(alias)}`
    );
  },
};

/**
 * 认证工具函数
 */
export const authUtils = {
  /**
   * 保存认证令牌
   * @param token 认证令牌
   */
  saveToken(token: string): void {
    localStorage.setItem('token', token);
  },

  /**
   * 获取认证令牌
   * @returns 认证令牌或null
   */
  getToken(): string | null {
    return localStorage.getItem('token');
  },

  /**
   * 清除认证令牌
   */
  clearToken(): void {
    localStorage.removeItem('token');
  },

  /**
   * 检查是否已登录
   * @returns 是否已登录
   */
  isAuthenticated(): boolean {
    return !!this.getToken();
  },

  /**
   * 保存用户信息
   * @param userInfo 用户信息
   */
  saveUserInfo(userInfo: AuthResponse): void {
    localStorage.setItem('userInfo', JSON.stringify(userInfo));
  },

  /**
   * 获取用户信息
   * @returns 用户信息或null
   */
  getUserInfo(): AuthResponse | null {
    const userInfo = localStorage.getItem('userInfo');
    return userInfo ? JSON.parse(userInfo) : null;
  },

  /**
   * 清除用户信息
   */
  clearUserInfo(): void {
    localStorage.removeItem('userInfo');
  },

  /**
   * 登出
   */
  logout(): void {
    this.clearToken();
    this.clearUserInfo();
    // 可以在这里添加其他登出逻辑，如清除其他缓存数据
  },

  /**
   * 处理登录成功
   * @param authResponse 认证响应
   */
  handleLoginSuccess(authResponse: AuthResponse): void {
    this.saveToken(authResponse.token);
    this.saveUserInfo(authResponse);
  },
};

// 导出默认对象以保持向后兼容
export default authApi;
