import { useTranslation } from 'react-i18next';
import { useAppStore } from '../store';
import type { Language } from '../store/appStore';
import { SUPPORTED_LANGUAGES } from '@/locales';

/**
 * 语言切换自定义Hook
 * 提供便捷的语言切换功能
 */
export const useLanguage = () => {
  const { i18n, t } = useTranslation();
  const { language, setLanguage } = useAppStore();

  /**
   * 切换语言
   * @param newLanguage 新的语言代码
   */
  const changeLanguage = (newLanguage: Language) => {
    setLanguage(newLanguage);
  };

  /**
   * 获取当前语言显示名称
   */
  const getCurrentLanguageName = () => {
    return SUPPORTED_LANGUAGES.find(item => item.code === language)?.name;
  };

  /**
   * 检查是否为中文
   */
  const isZhCN = language === 'zh';

  /**
   * 检查是否为英文
   */
  const isEnUS = language === 'en';

  return {
    // 状态
    language,
    isZhCN,
    isEnUS,

    // 方法
    t,
    i18n,
    changeLanguage,
    getCurrentLanguageName,
  };
};
