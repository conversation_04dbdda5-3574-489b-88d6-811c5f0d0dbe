import { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '../store/authStore';
import { message } from 'antd';

interface UseRouteGuardOptions {
  requiresAuth?: boolean;
  allowedRoles?: string[];
  redirectTo?: string;
  guestOnly?: boolean; // 只允许游客访问
}

/**
 * 路由守卫Hook - 在组件内部使用
 * 类似Vue的组件内守卫
 */
export const useRouteGuard = (options: UseRouteGuardOptions = {}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { isAuthenticated, user } = useAuthStore();

  const {
    requiresAuth = false,
    allowedRoles = [],
    redirectTo = '/',
    guestOnly = false,
  } = options;

  useEffect(() => {
    // 游客专用页面检查
    if (guestOnly && isAuthenticated) {
      message.info('您已登录，无需访问此页面');
      navigate(redirectTo, { replace: true });
      return;
    }

    // 需要登录的页面检查
    if (requiresAuth && !isAuthenticated) {
      message.warning('请先登录');
      navigate('/login', {
        replace: true,
        state: { from: location },
      });
      return;
    }

    // 角色权限检查
    if (allowedRoles.length > 0 && user && !allowedRoles.includes(user.role)) {
      message.error('您没有权限访问此页面');
      navigate(redirectTo, { replace: true });
      return;
    }
  }, [
    isAuthenticated,
    user,
    navigate,
    location,
    requiresAuth,
    allowedRoles,
    redirectTo,
    guestOnly,
  ]);

  return {
    isAuthenticated,
    user,
    isAuthorized:
      !requiresAuth ||
      (isAuthenticated &&
        (allowedRoles.length === 0 ||
          (user && allowedRoles.includes(user.role)))),
  };
};
